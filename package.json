{"name": "assmt-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@chakra-ui/react": "^3.20.0", "@emotion/react": "^11.14.0", "@hookform/resolvers": "^5.1.1", "@tanstack/react-query": "^5.80.6", "axios": "^1.9.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "next": "15.3.3", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "react-icons": "^5.5.0", "yup": "^1.6.1"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "typescript": "^5"}}