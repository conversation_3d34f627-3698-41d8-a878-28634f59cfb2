import { CloseButton, <PERSON><PERSON>, <PERSON><PERSON>ta<PERSON>, <PERSON>, Spinner } from "@chakra-ui/react";
import DefaultButton from "../buttons/button";
import { useEffect, useRef } from "react";

export interface BasicModalProps {
  placement?: "top" | "bottom" | "center";
  open: boolean;
  setOpen: (open: boolean) => void;
  children?: React.ReactNode;
  title?: string;
  cancelText?: string;
  confirmText?: string;
  deleteText?: string;
  handleDelete?: () => void;
  handleConfirm?: () => void;
  asForm?: boolean;
  handleSubmit?: (e: React.FormEvent<HTMLFormElement>) => void;
  isSubmitting?: boolean;
  size?: "sm" | "md" | "lg" | "xl" | "xs" | "cover" | "full";
  closeOnInteractOutside?: boolean;
  closeOnEscape?: boolean;
  customFooter?: React.ReactNode;
  hideFooter?: boolean;
  loading?: boolean;
  error?: string;
  success?: string;
  modalStyles?: Record<string, any>;
  confirmDisabled?: boolean;
  confirmButtonColor?: string;
  cancelButtonColor?: string;
}

export default function BasicModal({
  placement = "center",
  open,
  setOpen,
  children,
  title,
  cancelText = "Cancelar",
  confirmText = "Confirmar",
  deleteText,
  handleDelete,
  asForm = false,
  isSubmitting = false,
  handleSubmit,
  handleConfirm,
  size = "md",
  closeOnInteractOutside = true,
  closeOnEscape = true,
  customFooter,
  hideFooter = false,
  loading = false,
  error,
  success,
  modalStyles = {},
  confirmDisabled = false,
  confirmButtonColor = "#a5854a",
  cancelButtonColor = "gray.600",
}: BasicModalProps) {
  const initialFocusRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (!closeOnEscape || !open) return;

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setOpen(false);
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [closeOnEscape, open, setOpen]);

  const handleClose = () => {
    if (!isSubmitting) {
      setOpen(false);
    }
  };

  const handleBackdropClick = () => {
    if (closeOnInteractOutside && !isSubmitting) {
      setOpen(false);
    }
  };

  const renderContent = () => {
    if (loading) {
      return (
        <HStack justify="center" p={8}>
          <Spinner size="lg" color="blue.500" />
        </HStack>
      );
    }

    return children;
  };

  const renderFooter = () => {
    if (hideFooter) return null;
    if (customFooter) return customFooter;

    return (
      <Dialog.Footer>
        <HStack gap={3} justify="flex-end" w="100%">
          <DefaultButton
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting}
            buttonColor={cancelButtonColor}
            size="sm"
          >
            {cancelText}
          </DefaultButton>

          {deleteText && handleDelete && (
            <DefaultButton
              onClick={handleDelete}
              disabled={isSubmitting}
              buttonColor="red.500"
              size="sm"
            >
              {deleteText}
            </DefaultButton>
          )}

          {confirmText && handleConfirm && (
            <DefaultButton
              onClick={handleConfirm}
              disabled={confirmDisabled || isSubmitting}
              buttonColor={confirmButtonColor}
              size="sm"
            >
              {isSubmitting ? <Spinner size="sm" mr={2} /> : null}
              {confirmText}
            </DefaultButton>
          )}

          {asForm && (
            <DefaultButton
              type="submit"
              disabled={confirmDisabled || isSubmitting}
              buttonColor={confirmButtonColor}
              size="sm"
            >
              {isSubmitting ? <Spinner size="sm" mr={2} /> : null}
              {confirmText}
            </DefaultButton>
          )}
        </HStack>
      </Dialog.Footer>
    );
  };

  if (!open) return null;

  return (
    <Portal>
      <Dialog.Root
        open={open}
        onOpenChange={(details) => {
          if (!details.open && !isSubmitting) {
            setOpen(false);
          }
        }}
        placement={placement}
        motionPreset="slide-in-bottom"
        size={size}
        closeOnInteractOutside={closeOnInteractOutside && !isSubmitting}
        closeOnEscape={closeOnEscape && !isSubmitting}
      >
        <Dialog.Backdrop
          onClick={handleBackdropClick}
          bg="blackAlpha.600"
          backdropFilter="blur(4px)"
        />
        <Dialog.Positioner>
          <Dialog.Content
            zIndex={"tooltip"}
            bg="gray.800"
            border="1px solid"
            borderColor="gray.600"
            borderRadius="lg"
            boxShadow="xl"
            maxH="90vh"
            overflow="hidden"
            {...modalStyles}
            as={asForm ? "form" : "div"}
            onSubmit={asForm ? handleSubmit : undefined}
            role="dialog"
            aria-modal="true"
            aria-labelledby={title ? "modal-title" : undefined}
          >
            {title && (
              <Dialog.Header
                bg="gray.700"
                borderBottom="1px solid"
                borderColor="gray.600"
                p={4}
              >
                <Dialog.Title
                  id="modal-title"
                  fontSize="lg"
                  fontWeight="bold"
                  color="white"
                >
                  {title}
                </Dialog.Title>
              </Dialog.Header>
            )}

            <Dialog.Body p={4} maxH="60vh" overflowY="auto" color="white">
              {error && (
                <HStack
                  bg="red.500"
                  color="white"
                  p={3}
                  borderRadius="md"
                  mb={4}
                >
                  {error}
                </HStack>
              )}

              {success && (
                <HStack
                  bg="green.500"
                  color="white"
                  p={3}
                  borderRadius="md"
                  mb={4}
                >
                  {success}
                </HStack>
              )}

              {renderContent()}
            </Dialog.Body>

            {renderFooter()}

            <Dialog.CloseTrigger asChild>
              <CloseButton
                position="absolute"
                top={2}
                right={2}
                size="sm"
                color="gray.400"
                _hover={{ color: "white" }}
                disabled={isSubmitting}
                ref={initialFocusRef}
              />
            </Dialog.CloseTrigger>
          </Dialog.Content>
        </Dialog.Positioner>
      </Dialog.Root>
    </Portal>
  );
}
