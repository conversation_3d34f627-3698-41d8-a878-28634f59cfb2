"use client";
import Default<PERSON>utton from "@/components/global/buttons/button";
import BasicModal from "@/components/global/modal/basic-modal";
import { translateCategoryType } from "@/utils/formatters/formatCategoriesType";
import {
  Box,
  Button,
  Flex,
  HStack,
  IconButton,
  Input,
  Stack,
  Table,
  Text,
  Badge,
  useDisclosure,
  VStack,
  Field,
  Combobox,
  useFilter,
  useListCollection,
  Portal,
} from "@chakra-ui/react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useState } from "react";
import { useForm } from "react-hook-form";
import {
  LuTrash2,
  LuPlus,
  LuSearch,
  LuDelete,
  LuPen,
  LuPenLine,
  LuPencil,
} from "react-icons/lu";
import * as yup from "yup";
import { formatInTimeZone } from "date-fns-tz";
import { useMutation } from "@tanstack/react-query";
import { api } from "@/services/api";
import { toaster } from "@/components/ui/toaster";
import { useGetAllCategories } from "@/hook/hierarchy/useGetCategories";
import { queryClient } from "@/services/queryClient";

const categoriesTypes = [
  { label: "Segmento", value: "SEGMENT" },
  { label: "Superintendência", value: "SUPERINTENDENCY" },
  { label: "Gerência", value: "MANAGEMENT" },
  { label: "Cargo", value: "POSITION" },
];

const NewCategorySchema = yup.object().shape({
  name: yup
    .string()
    .min(3, "O nome deve ter no mínimo 3 caracteres")
    .max(50, "O nome deve ter no máximo 50 caracteres")
    .required("O nome da categoria é obrigatório"),
  type: yup
    .string()
    .oneOf(
      ["SEGMENT", "SUPERINTENDENCY", "MANAGEMENT", "POSITION"],
      "Tipo de categoria inválido"
    )
    .required("O tipo da categoria é obrigatório"),
});

type NewCategoryFormData = yup.InferType<typeof NewCategorySchema>;

export default function Categories() {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<any>(null);

  const { data: categoriesData, isLoading, isFetching } = useGetAllCategories();

  const { contains } = useFilter({ sensitivity: "base" });

  const { collection, filter } = useListCollection({
    initialItems: categoriesTypes,
    filter: contains,
  });

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<NewCategoryFormData>({
    resolver: yupResolver(NewCategorySchema),
    defaultValues: {
      name: "",
      type: undefined,
    },
  });

  const watchedType = watch("type");

  const addCategory = useMutation({
    mutationFn: async (data: NewCategoryFormData) => {
      await api.post("/management/hierarchy", data);
    },
    onSuccess: () => {
      toaster.success({
        title: "Categoria adicionada com sucesso!",
      });
    },
  });

  const editCategory = useMutation({
    mutationFn: async (data: NewCategoryFormData) => {
      await api.patch(
        `/management/hierarchy/${selectedCategory?.secureId}`,
        data
      );
    },
    onSuccess: () => {
      toaster.success({
        title: "Categoria editada com sucesso!",
      });
    },
  });

  const handleOpenAddModal = () => {
    filter("");
    reset();
    setIsAddModalOpen(true);
  };

  const handleOpenEditModal = (category: any) => {
    filter("");
    setSelectedCategory(category);
    setValue("name", category.name);
    setValue("type", category.type as any);
    setIsEditModalOpen(true);
  };

  const handleOpenDeleteModal = (category: any) => {
    setSelectedCategory(category);
    setIsDeleteModalOpen(true);
  };

  const handleAddCategory = async (data: NewCategoryFormData) => {
    try {
      await addCategory.mutateAsync(data);
      queryClient.invalidateQueries({ queryKey: ["categories"] });
      setIsAddModalOpen(false);
      filter("");
      reset();
    } catch (e) {
      console.log("Erro ao adicionar categoria", e);
    }
  };

  const handleEditCategory = async (data: NewCategoryFormData) => {
    try {
      await editCategory.mutateAsync(data);
      queryClient.invalidateQueries({ queryKey: ["categories"] });
      setIsEditModalOpen(false);
      filter("");
      reset();
    } catch (e) {
      console.log("Erro ao editar categoria", e);
    }
  };

  const handleDeleteCategory = async () => {
    console.log("Delete Category:", selectedCategory?.secureId);
  };

  return (
    <Flex flex={1} position={"relative"} overflow={"hidden"} p={4}>
      <Stack w="100%" gap={6}>
        {/* Header */}
        <Text fontSize="2xl" fontWeight="bold" color="white">
          Categorias
        </Text>

        {/* Search and Filters */}
        <HStack gap={4} justify="space-between">
          <Box position="relative" flex={1} maxW="400px">
            <Input
              placeholder="Buscar..."
              bg="gray.800"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
            />
            <Box
              position="absolute"
              right={3}
              top="50%"
              transform="translateY(-50%)"
              color="gray.400"
            >
              <LuSearch />
            </Box>
          </Box>
          <DefaultButton onClick={handleOpenAddModal} size="md">
            <LuPlus />
            Adicionar Categoria
          </DefaultButton>
        </HStack>

        {/* Table */}
        <Box
          bg="gray.800"
          borderRadius="lg"
          border="1px solid"
          borderColor="gray.600"
          overflow="hidden"
        >
          <Table.Root size="md" variant="outline">
            <Table.Header bg="gray.700">
              <Table.Row>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Nome
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Tipo
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Data de Criação
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  <Flex justify="center">Ações</Flex>
                </Table.ColumnHeader>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {categoriesData?.data?.map((item) => (
                <Table.Row key={item.secureId} _hover={{ bg: "gray.700" }}>
                  <Table.Cell>
                    <Text color="gray.300" fontWeight="medium">
                      {item.name}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300" maxW="200px">
                      {translateCategoryType(item.type)}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300" maxW="200px">
                      {formatInTimeZone(
                        item.createdAt,
                        "America/Sao_Paulo",
                        "dd/MM/yyyy HH:mm:ss"
                      )}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <HStack gap={2} justifyContent={"center"}>
                      <DefaultButton
                        tooltipContent="Editar"
                        buttonColor="#156082"
                        size="sm"
                        onClick={() => handleOpenEditModal(item)}
                      >
                        <LuPencil />
                      </DefaultButton>
                      <DefaultButton
                        tooltipContent="Excluir"
                        buttonColor="red.500"
                        size="sm"
                        onClick={() => handleOpenDeleteModal(item)}
                      >
                        <LuTrash2 />
                      </DefaultButton>
                    </HStack>
                  </Table.Cell>
                </Table.Row>
              )) || (
                <Table.Row>
                  <Table.Cell colSpan={4}>
                    <Text color="gray.400" textAlign="center" py={4}>
                      Nenhuma categoria encontrada
                    </Text>
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table.Root>
        </Box>
      </Stack>

      {/* Add Category Modal */}
      <BasicModal
        open={isAddModalOpen}
        setOpen={setIsAddModalOpen}
        title="Adicionar Nova Categoria"
        size="md"
        asForm={true}
        handleSubmit={handleSubmit(handleAddCategory)}
        isSubmitting={isSubmitting}
        confirmText="Adicionar"
        cancelText="Cancelar"
        placement="center"
      >
        <VStack gap={4} align="stretch">
          <Field.Root invalid={!!errors.name}>
            <Field.Label color="white">Nome da Categoria</Field.Label>
            <Input
              placeholder="Digite o nome da categoria"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("name")}
            />
            <Field.ErrorText>{errors.name?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.type}>
            <Field.Label color="white">Tipo da Categoria</Field.Label>
            <Combobox.Root
              collection={collection}
              onInputValueChange={(e) => filter(e.inputValue)}
              onValueChange={(e) => {
                setValue("type", e.value[0] as any);
              }}
              value={watchedType ? [watchedType] : []}
              openOnClick
            >
              <Combobox.Control>
                <Combobox.Input
                  placeholder="Selecione o tipo"
                  bg="gray.700"
                  border="1px solid"
                  borderColor="gray.600"
                  color="white"
                  _placeholder={{ color: "gray.400" }}
                  _focus={{
                    borderColor: "blue.400",
                    boxShadow: "0 0 0 1px #3182ce",
                  }}
                />
                <Combobox.IndicatorGroup>
                  <Combobox.ClearTrigger />
                  <Combobox.Trigger />
                </Combobox.IndicatorGroup>
              </Combobox.Control>
              <Portal>
                <Combobox.Positioner>
                  <Combobox.Content
                    zIndex={"popover"}
                    bg="gray.700"
                    border="1px solid"
                    borderColor="gray.600"
                  >
                    <Combobox.Empty color="gray.400">
                      Nenhum item encontrado
                    </Combobox.Empty>
                    {collection.items.map((item) => (
                      <Combobox.Item
                        item={item}
                        key={item.value}
                        color="white"
                        _hover={{ bg: "gray.600" }}
                        _selected={{ bg: "blue.600" }}
                      >
                        {item.label}
                        <Combobox.ItemIndicator />
                      </Combobox.Item>
                    ))}
                  </Combobox.Content>
                </Combobox.Positioner>
              </Portal>
            </Combobox.Root>
            <Field.ErrorText>{errors.type?.message}</Field.ErrorText>
          </Field.Root>
        </VStack>
      </BasicModal>

      {/* Edit Category Modal */}
      <BasicModal
        open={isEditModalOpen}
        setOpen={setIsEditModalOpen}
        title="Editar Categoria"
        size="md"
        asForm={true}
        handleSubmit={handleSubmit(handleEditCategory)}
        isSubmitting={isSubmitting}
        confirmText="Salvar"
        cancelText="Cancelar"
        placement="center"
      >
        <VStack gap={4} align="stretch">
          <Field.Root invalid={!!errors.name}>
            <Field.Label color="white">Nome da Categoria</Field.Label>
            <Input
              placeholder="Digite o nome da categoria"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("name")}
            />
            <Field.ErrorText>{errors.name?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.type}>
            <Field.Label color="white">Tipo da Categoria</Field.Label>
            <Combobox.Root
              collection={collection}
              onInputValueChange={(e) => filter(e.inputValue)}
              onValueChange={(e) => {
                setValue("type", e.value[0] as any);
              }}
              value={watchedType ? [watchedType] : []}
              openOnClick
            >
              <Combobox.Control>
                <Combobox.Input
                  placeholder="Selecione o tipo"
                  bg="gray.700"
                  border="1px solid"
                  borderColor="gray.600"
                  color="white"
                  _placeholder={{ color: "gray.400" }}
                  _focus={{
                    borderColor: "blue.400",
                    boxShadow: "0 0 0 1px #3182ce",
                  }}
                />
                <Combobox.IndicatorGroup>
                  <Combobox.ClearTrigger />
                  <Combobox.Trigger />
                </Combobox.IndicatorGroup>
              </Combobox.Control>
              <Portal>
                <Combobox.Positioner>
                  <Combobox.Content
                    zIndex={"popover"}
                    bg="gray.700"
                    border="1px solid"
                    borderColor="gray.600"
                  >
                    <Combobox.Empty color="gray.400">
                      Nenhum item encontrado
                    </Combobox.Empty>
                    {collection.items.map((item) => (
                      <Combobox.Item
                        item={item}
                        key={item.value}
                        color="white"
                        _hover={{ bg: "gray.600" }}
                        _selected={{ bg: "blue.600" }}
                      >
                        {item.label}
                        <Combobox.ItemIndicator />
                      </Combobox.Item>
                    ))}
                  </Combobox.Content>
                </Combobox.Positioner>
              </Portal>
            </Combobox.Root>
            <Field.ErrorText>{errors.type?.message}</Field.ErrorText>
          </Field.Root>
        </VStack>
      </BasicModal>

      {/* Delete Category Modal */}
      <BasicModal
        open={isDeleteModalOpen}
        setOpen={setIsDeleteModalOpen}
        title="Excluir Categoria"
        size="sm"
        handleConfirm={handleDeleteCategory}
        confirmText="Excluir"
        cancelText="Cancelar"
        placement="center"
        confirmButtonColor="red.500"
      >
        <VStack gap={4} align="center">
          <Text fontSize="md" color="white" textAlign="center">
            Você tem certeza que deseja excluir a categoria{" "}
            <Text as="span" fontWeight="bold" color="red.400">
              "{selectedCategory?.name}"
            </Text>
            ?
          </Text>
          <Text fontSize="sm" color="gray.400" textAlign="center">
            Esta ação não pode ser desfeita.
          </Text>
        </VStack>
      </BasicModal>
    </Flex>
  );
}
